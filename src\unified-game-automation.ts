/**
 * Unified Game Automation Script - EntityController & GoodyHutHelper
 * Combines EntityController auto-upgrade functionality with GoodyHutHelper collection and cleanup
 * Target: libil2cpp.so
 * Classes: Entity<PERSON>ontroller, GoodyHutHelper (empty namespace)
 * Assembly: Assembly-CSharp.dll
 */

import "frida-il2cpp-bridge";

interface EntityControllerMethods {
    IsSelected: Il2Cpp.Method | null;
    CanUpgrade: Il2Cpp.Method | null;
    GetLevel: Il2Cpp.Method | null;
    GetMaxLevel: Il2Cpp.Method | null;
    GetMaxUpgradeLevel: Il2Cpp.Method | null;
    InstantUpgrade: Il2Cpp.Method | null;
    Select: Il2Cpp.Method | null;
    Unselect: Il2Cpp.Method | null;
    GetUniqueId: Il2Cpp.Method | null;
    IsUpgrading: Il2Cpp.Method | null;
    GetUpgradeTime: Il2Cpp.Method | null;
    GetUpgradeCost: Il2Cpp.Method | null;
}

interface GoodyHutHelperMethods {
    CanCollect: Il2Cpp.Method | null;
    FinishCollect: Il2Cpp.Method | null;
    Config: Il2Cpp.Method | null;
    Update: Il2Cpp.Method | null;
    SellRuins: Il2Cpp.Method | null;
    GetRewardType: Il2Cpp.Method | null;
    GetRewardAmount: Il2Cpp.Method | null;
}

interface GoodyHutHelperConfig {
    cleanUpOffset: number;
    stateMachineOffset: number;
}

interface CollectionTracker {
    instanceId: string;
    discoveryTime: number;
    retryCount: number;
    lastFailTime: number;
    processed: boolean;
}

interface UpgradeTracker {
    entityId: string;
    levelBefore: number;
    upgradeStartTime: number;
    upgradeCallCount: number;
    lastLevelCheck: number;
    expectedLevel: number;
}

interface AutomationStats {
    totalInstances: number;
    selectedInstances: number;
    upgradeableInstances: number;
    upgradesPerformed: number;
    startTime: number;
}

class UnifiedGameAutomation {
    private assemblyImage: Il2Cpp.Image | null = null;
    private entityControllerClass: Il2Cpp.Class | null = null;
    private goodyHutHelperClass: Il2Cpp.Class | null = null;
    private il2cppModule: Module | null = null;
    
    private entityMethods: EntityControllerMethods = {
        IsSelected: null,
        CanUpgrade: null,
        GetLevel: null,
        GetMaxLevel: null,
        GetMaxUpgradeLevel: null,
        InstantUpgrade: null,
        Select: null,
        Unselect: null,
        GetUniqueId: null,
        IsUpgrading: null,
        GetUpgradeTime: null,
        GetUpgradeCost: null
    };
    
    private goodyHutMethods: GoodyHutHelperMethods = {
        CanCollect: null,
        FinishCollect: null,
        Config: null,
        Update: null,
        SellRuins: null,
        GetRewardType: null,
        GetRewardAmount: null
    };
    
    private config: GoodyHutHelperConfig = {
        cleanUpOffset: 0x30,
        stateMachineOffset: 0x18
    };
    
    private stats: AutomationStats = {
        totalInstances: 0,
        selectedInstances: 0,
        upgradeableInstances: 0,
        upgradesPerformed: 0,
        startTime: Date.now()
    };

    // Enhanced upgrade tracking system
    private upgradeTrackers: Map<string, UpgradeTracker> = new Map();
    private upgradeCallbacks: Map<string, (success: boolean, newLevel: number) => void> = new Map();
    
    // Collection tracking system
    private collectionTrackers: Map<string, CollectionTracker> = new Map();
    private processedInstances: Set<string> = new Set();
    private failedInstances: Map<string, CollectionTracker> = new Map();
    
    // Collection configuration
    private readonly BATCH_SIZE = 15;
    private readonly BATCH_INTERVAL = 3000;
    private readonly RETRY_LIMIT = 3;
    private readonly DISCOVERY_TIMEOUT = 10000;

    // Selling configuration
    private readonly SELL_DELAY_MIN = 1000; // Minimum delay before selling (1 second)
    private readonly SELL_DELAY_MAX = 3000; // Maximum delay before selling (3 seconds)
    private readonly SELL_RETRY_LIMIT = 2;

    // Selling tracking
    private pendingSales: Map<string, NodeJS.Timeout> = new Map();
    private soldInstances: Set<string> = new Set();
    private sellFailures: Map<string, number> = new Map();

    constructor() {
        console.log("🚀 Starting Unified Game Automation (EntityController + GoodyHutHelper)...");
    }

    /**
     * Initialize IL2CPP and find required classes
     */
    public async initialize(): Promise<boolean> {
        try {
            console.log("🔍 Initializing IL2CPP domain and detecting module...");

            // First, detect the IL2CPP module
            if (!this.detectIL2CPPModule()) {
                console.log("❌ Failed to detect IL2CPP module");
                return false;
            }

            // Get Assembly-CSharp
            this.assemblyImage = Il2Cpp.domain.assembly("Assembly-CSharp").image;
            if (!this.assemblyImage) {
                console.log("❌ Failed to get Assembly-CSharp image");
                this.listAvailableAssemblies();
                return false;
            }

            console.log("✅ Assembly-CSharp found");

            // Find EntityController class
            this.entityControllerClass = this.assemblyImage.class("EntityController");
            if (!this.entityControllerClass) {
                console.log("❌ EntityController class not found");
                this.listAvailableClasses();
                return false;
            }

            console.log("✅ EntityController class found");

            // Find GoodyHutHelper class
            this.goodyHutHelperClass = this.assemblyImage.class("GoodyHutHelper");
            if (!this.goodyHutHelperClass) {
                console.log("❌ GoodyHutHelper class not found");
                this.listAvailableClasses();
                return false;
            }

            console.log("✅ GoodyHutHelper class found");

            // Setup method hooks for both classes
            this.setupEntityControllerMethods();
            this.setupGoodyHutHelperMethods();

            // Setup automation functions
            this.setupGlobalFunctions();

            console.log("🎯 Unified automation hook setup complete!");

            return true;

        } catch (error) {
            console.log(`❌ Initialization failed: ${error}`);
            return false;
        }
    }

    /**
     * Detect IL2CPP module with enhanced logic
     */
    private detectIL2CPPModule(): boolean {
        try {
            console.log("🔍 Detecting IL2CPP module...");
            
            let potentialModules: Module[] = [];
            
            Process.enumerateModules().forEach((module) => {
                const moduleName = module.name.toLowerCase();
                
                // Check for obvious IL2CPP patterns first
                if (moduleName.includes("il2cpp") || 
                    moduleName.includes("libil2cpp") ||
                    moduleName === "libil2cpp.so") {
                    this.il2cppModule = module;
                    console.log(`✅ Found IL2CPP module: ${module.name} at ${module.base}`);
                    return;
                }
                
                // Collect potential candidates (large modules)
                if (module.size > 5000000) { // > 5MB
                    potentialModules.push(module);
                }
            });
            
            // If no obvious module found, try the largest potential candidate
            if (!this.il2cppModule && potentialModules.length > 0) {
                potentialModules.sort((a, b) => b.size - a.size);
                this.il2cppModule = potentialModules[0];
                console.log(`⚠️ Using largest module as IL2CPP candidate: ${this.il2cppModule.name} (${(this.il2cppModule.size/1024/1024).toFixed(1)}MB)`);
            }
            
            if (!this.il2cppModule) {
                console.log("❌ No suitable IL2CPP module found");
                this.listLargeModules();
                return false;
            }
            
            return true;
        } catch (error) {
            console.log(`❌ Error detecting IL2CPP module: ${error}`);
            return false;
        }
    }

    /**
     * Setup EntityController method references and hooks
     */
    private setupEntityControllerMethods(): void {
        try {
            console.log("🔧 Setting up EntityController method references...");

            // Core methods
            this.entityMethods.IsSelected = this.entityControllerClass!.method("IsSelected");
            this.entityMethods.CanUpgrade = this.entityControllerClass!.method("CanUpgrade");
            this.entityMethods.GetLevel = this.entityControllerClass!.method("GetLevel");
            this.entityMethods.GetMaxLevel = this.entityControllerClass!.method("GetMaxLevel");
            this.entityMethods.GetMaxUpgradeLevel = this.entityControllerClass!.method("GetMaxUpgradeLevel");
            this.entityMethods.InstantUpgrade = this.entityControllerClass!.method("InstantUpgrade");

            // Additional methods
            try {
                this.entityMethods.Select = this.entityControllerClass!.method("Select");
                this.entityMethods.Unselect = this.entityControllerClass!.method("Unselect");
                this.entityMethods.GetUniqueId = this.entityControllerClass!.method("get_uniqueId");
                this.entityMethods.IsUpgrading = this.entityControllerClass!.method("IsUpgrading");
                this.entityMethods.GetUpgradeTime = this.entityControllerClass!.method("GetUpgradeTime");
                this.entityMethods.GetUpgradeCost = this.entityControllerClass!.method("GetUpgradeCost");
            } catch (error) {
                console.log(`⚠️ Some optional EntityController methods not found: ${error}`);
            }

            // Verify methods found
            Object.entries(this.entityMethods).forEach(([methodName, method]) => {
                if (method) {
                    console.log(`✅ Found EntityController method: ${methodName}`);
                } else {
                    console.log(`⚠️ EntityController method not found: ${methodName}`);
                }
            });

            // Hook InstantUpgrade for monitoring
            this.hookInstantUpgrade();

        } catch (error) {
            console.log(`❌ EntityController method setup failed: ${error}`);
        }
    }

    /**
     * Setup GoodyHutHelper method references and hooks
     */
    private setupGoodyHutHelperMethods(): void {
        try {
            console.log("🔧 Setting up GoodyHutHelper method references...");

            if (!this.il2cppModule) {
                console.log("❌ IL2CPP module not available for GoodyHutHelper setup");
                return;
            }

            // Setup method references using RVA offsets
            const baseAddr = this.il2cppModule.base;

            // Create NativeFunction references for GoodyHutHelper methods
            try {
                this.goodyHutMethods.CanCollect = new NativeFunction(baseAddr.add(0x209D258), 'int', ['pointer']) as any;
                this.goodyHutMethods.FinishCollect = new NativeFunction(baseAddr.add(0x209B924), 'void', ['pointer']) as any;
                this.goodyHutMethods.Config = new NativeFunction(baseAddr.add(0x209B54C), 'pointer', ['pointer']) as any;
                this.goodyHutMethods.Update = new NativeFunction(baseAddr.add(0x209CF60), 'void', ['pointer']) as any;
                this.goodyHutMethods.SellRuins = new NativeFunction(baseAddr.add(0x209DE3C), 'void', ['pointer']) as any;
                this.goodyHutMethods.GetRewardType = new NativeFunction(baseAddr.add(0x209CC3C), 'int', ['pointer']) as any;
                this.goodyHutMethods.GetRewardAmount = new NativeFunction(baseAddr.add(0x209D9C4), 'int', ['pointer']) as any;

                console.log("✅ GoodyHutHelper method references created");
            } catch (error) {
                console.log(`⚠️ Some GoodyHutHelper methods could not be created: ${error}`);
            }

            // Setup collection hooks
            this.setupCollectionHooks();

        } catch (error) {
            console.log(`❌ GoodyHutHelper method setup failed: ${error}`);
        }
    }

    /**
     * Setup collection hooks for automatic ruins collection
     */
    private setupCollectionHooks(): void {
        if (!this.il2cppModule || !this.goodyHutMethods.CanCollect) {
            console.log("❌ Cannot setup collection hooks - missing dependencies");
            return;
        }

        try {
            console.log("🎣 Setting up collection hooks...");

            const canCollectAddr = this.il2cppModule.base.add(0x209D258);

            // Hook CanCollect to auto-trigger collection
            Interceptor.attach(canCollectAddr, {
                onEnter: function(args) {
                    this.thisPtr = args[0]; // 'this' pointer (GoodyHutHelper instance)
                },

                onLeave: (retval) => {
                    const canCollect = retval.toInt32();

                    if (canCollect === 1) { // true in IL2CPP
                        console.log("[+] Ruins can be collected! Auto-triggering...");

                        try {
                            const thisPtr = (this as any).thisPtr;
                            this.processCollectibleInstance(thisPtr);
                        } catch (e) {
                            console.log(`[-] Error in auto-collection: ${e}`);
                        }
                    }
                }
            });

            console.log("✅ Collection hooks installed");
        } catch (error) {
            console.log(`❌ Failed to setup collection hooks: ${error}`);
        }
    }

    /**
     * Process a collectible GoodyHutHelper instance with automatic selling
     */
    private processCollectibleInstance(thisPtr: NativePointer): void {
        try {
            const instanceId = thisPtr.toString();

            // Check if already processed
            if (this.processedInstances.has(instanceId)) {
                return;
            }

            console.log(`[+] Processing collectible ruins at: ${thisPtr}`);

            // Step 1: Get config and set cleanUp flag for automatic selling
            let configPtr: NativePointer | null = null;
            if (this.goodyHutMethods.Config) {
                try {
                    configPtr = (this.goodyHutMethods.Config as any)(thisPtr);

                    if (configPtr && !configPtr.isNull()) {
                        // Set cleanUp flag at offset 0x30 to enable automatic selling
                        const cleanUpAddr = configPtr.add(this.config.cleanUpOffset);

                        // Read current value for logging
                        const currentCleanUpValue = cleanUpAddr.readU8();
                        console.log(`[*] Current cleanUp flag value: ${currentCleanUpValue} for ${instanceId}`);

                        // Set cleanUp flag to true (1)
                        cleanUpAddr.writeU8(1);

                        // Verify the flag was set
                        const newCleanUpValue = cleanUpAddr.readU8();
                        console.log(`[+] Set cleanUp flag: ${currentCleanUpValue} → ${newCleanUpValue} for ${instanceId}`);

                        if (newCleanUpValue !== 1) {
                            console.log(`[!] Warning: cleanUp flag may not have been set correctly for ${instanceId}`);
                        }
                    } else {
                        console.log(`[-] Config() returned null/invalid pointer for ${instanceId}`);
                    }
                } catch (e) {
                    console.log(`[-] Error setting cleanUp flag for ${instanceId}: ${e}`);
                }
            }

            // Step 2: Execute collection
            if (this.goodyHutMethods.FinishCollect) {
                try {
                    console.log(`[*] Calling FinishCollect() for ${instanceId}...`);
                    (this.goodyHutMethods.FinishCollect as any)(thisPtr);

                    // Mark as processed
                    this.processedInstances.add(instanceId);
                    console.log(`[+] Successfully collected ruins at ${thisPtr}`);

                    // Step 3: Schedule automatic selling with configurable delay
                    this.scheduleAutomaticSelling(thisPtr, instanceId);

                } catch (e) {
                    console.log(`[-] Error executing FinishCollect for ${instanceId}: ${e}`);

                    // Add to failed instances for retry
                    this.failedInstances.set(instanceId, {
                        instanceId,
                        discoveryTime: Date.now(),
                        retryCount: 1,
                        lastFailTime: Date.now(),
                        processed: false
                    });
                }
            } else {
                console.log(`[-] FinishCollect method not available for ${instanceId}`);
            }

        } catch (error) {
            console.log(`[-] Error processing collectible instance: ${error}`);
        }
    }

    /**
     * Schedule automatic selling of ruins after collection with configurable delay
     */
    private scheduleAutomaticSelling(thisPtr: NativePointer, instanceId: string): void {
        try {
            // Cancel any existing pending sale for this instance
            if (this.pendingSales.has(instanceId)) {
                clearTimeout(this.pendingSales.get(instanceId)!);
                this.pendingSales.delete(instanceId);
            }

            // Calculate random delay to avoid detection patterns
            const randomDelay = Math.floor(Math.random() * (this.SELL_DELAY_MAX - this.SELL_DELAY_MIN + 1)) + this.SELL_DELAY_MIN;

            console.log(`[*] Scheduling automatic selling for ${instanceId} in ${randomDelay}ms...`);

            // Schedule the selling operation
            const sellTimeout = setTimeout(() => {
                this.executeAutomaticSelling(thisPtr, instanceId);
                this.pendingSales.delete(instanceId);
            }, randomDelay);

            // Track the pending sale
            this.pendingSales.set(instanceId, sellTimeout);

        } catch (error) {
            console.log(`[-] Error scheduling automatic selling for ${instanceId}: ${error}`);
        }
    }

    /**
     * Execute automatic selling of ruins with error handling and retry logic
     */
    private executeAutomaticSelling(thisPtr: NativePointer, instanceId: string): void {
        try {
            // Check if already sold
            if (this.soldInstances.has(instanceId)) {
                console.log(`[*] Instance ${instanceId} already sold, skipping`);
                return;
            }

            console.log(`[+] Executing automatic selling for ruins at ${instanceId}...`);

            // Validate pointer is still valid
            if (!thisPtr || thisPtr.isNull()) {
                console.log(`[-] Invalid pointer for selling ${instanceId}, skipping`);
                return;
            }

            // Execute SellRuins() method
            if (this.goodyHutMethods.SellRuins) {
                try {
                    console.log(`[*] Calling SellRuins() for ${instanceId}...`);
                    (this.goodyHutMethods.SellRuins as any)(thisPtr);

                    // Mark as sold
                    this.soldInstances.add(instanceId);
                    console.log(`[+] ✅ Successfully sold ruins at ${instanceId} - FULLY AUTOMATED!`);

                    // Clear any failure tracking for this instance
                    this.sellFailures.delete(instanceId);

                } catch (sellError) {
                    const errorMsg = sellError instanceof Error ? sellError.message : String(sellError);
                    console.log(`[-] Error calling SellRuins() for ${instanceId}: ${errorMsg}`);

                    // Track failure and potentially retry
                    const currentFailures = this.sellFailures.get(instanceId) || 0;
                    this.sellFailures.set(instanceId, currentFailures + 1);

                    if (currentFailures < this.SELL_RETRY_LIMIT) {
                        console.log(`[*] Retrying automatic selling for ${instanceId} (attempt ${currentFailures + 2}/${this.SELL_RETRY_LIMIT + 1})`);

                        // Schedule retry with longer delay
                        setTimeout(() => {
                            this.executeAutomaticSelling(thisPtr, instanceId);
                        }, this.SELL_DELAY_MAX + 1000); // Extra delay for retry

                    } else {
                        console.log(`[-] Exceeded retry limit for selling ${instanceId}, abandoning`);
                    }
                }
            } else {
                console.log(`[-] SellRuins method not available for ${instanceId}`);
            }

        } catch (error) {
            console.log(`[-] Error in automatic selling execution for ${instanceId}: ${error}`);
        }
    }

    /**
     * Get collection and selling statistics
     */
    public getCollectionStats(): any {
        return {
            // Collection stats
            processed: this.processedInstances.size,
            failed: this.failedInstances.size,
            totalTracked: this.collectionTrackers.size,

            // Selling stats
            sold: this.soldInstances.size,
            pendingSales: this.pendingSales.size,
            sellFailures: this.sellFailures.size,

            // Timing
            startTime: this.stats.startTime,

            // Efficiency metrics
            collectionSuccessRate: this.processedInstances.size > 0 ?
                ((this.processedInstances.size / (this.processedInstances.size + this.failedInstances.size)) * 100).toFixed(1) + '%' : 'N/A',
            sellingSuccessRate: this.processedInstances.size > 0 ?
                ((this.soldInstances.size / this.processedInstances.size) * 100).toFixed(1) + '%' : 'N/A',
            fullyAutomatedCount: this.soldInstances.size // Collected AND sold automatically
        };
    }

    /**
     * Start automatic ruins collection and selling
     */
    public startRuinsCollection(): void {
        console.log("[+] ===== STARTING FULLY AUTOMATED RUINS SYSTEM =====");
        console.log("[+] 🏺 Automatic ruins collection: ACTIVE");
        console.log("[+] 💰 Automatic ruins selling: ACTIVE");
        console.log("[+] 🧹 Automatic cleanup (cleanUp flag): ACTIVE");
        console.log("[+] ⚡ Real-time processing: ENABLED");

        // Collection and selling are automatically started when hooks are installed
        console.log("[*] System Status:");
        console.log(`    - Collection hooks: ${this.goodyHutMethods.CanCollect ? 'INSTALLED' : 'MISSING'}`);
        console.log(`    - FinishCollect method: ${this.goodyHutMethods.FinishCollect ? 'AVAILABLE' : 'MISSING'}`);
        console.log(`    - SellRuins method: ${this.goodyHutMethods.SellRuins ? 'AVAILABLE' : 'MISSING'}`);
        console.log(`    - Config method: ${this.goodyHutMethods.Config ? 'AVAILABLE' : 'MISSING'}`);

        console.log("[*] Automation Flow:");
        console.log("    1. CanCollect() hook detects collectible ruins");
        console.log("    2. Set cleanUp flag at offset 0x30 in config");
        console.log("    3. Call FinishCollect() to collect ruins");
        console.log("    4. Schedule SellRuins() with random delay (1-3s)");
        console.log("    5. Execute automatic selling with retry logic");

        console.log("[+] ✅ FULLY AUTOMATED RUINS COLLECTION & SELLING IS NOW ACTIVE!");
        console.log("[*] Use getCollectionStats() to monitor progress");
    }

    /**
     * Stop automatic ruins collection and cancel pending sales
     */
    public stopRuinsCollection(): void {
        console.log("[*] ===== STOPPING AUTOMATED RUINS SYSTEM =====");

        // Cancel all pending sales
        let cancelledSales = 0;
        this.pendingSales.forEach((timeout, instanceId) => {
            clearTimeout(timeout);
            console.log(`[*] Cancelled pending sale for ${instanceId}`);
            cancelledSales++;
        });
        this.pendingSales.clear();

        console.log(`[*] Cancelled ${cancelledSales} pending sales`);
        console.log("[*] Note: Collection hooks remain active (cannot be disabled at runtime)");
        console.log("[*] New collections will still be processed, but selling is paused");
        console.log("[*] Use startRuinsCollection() to re-enable selling");
    }

    /**
     * Clear processed ruins cache and selling history
     */
    public clearProcessedRuins(): void {
        console.log("[*] ===== CLEARING RUINS AUTOMATION CACHE =====");

        // Cancel pending sales first
        let cancelledSales = 0;
        this.pendingSales.forEach((timeout) => {
            clearTimeout(timeout);
            cancelledSales++;
        });

        // Clear all tracking data
        const beforeStats = {
            processed: this.processedInstances.size,
            failed: this.failedInstances.size,
            sold: this.soldInstances.size,
            pending: this.pendingSales.size,
            sellFailures: this.sellFailures.size
        };

        this.processedInstances.clear();
        this.failedInstances.clear();
        this.collectionTrackers.clear();
        this.pendingSales.clear();
        this.soldInstances.clear();
        this.sellFailures.clear();

        console.log("[+] Cache cleared successfully:");
        console.log(`    - Processed instances: ${beforeStats.processed} → 0`);
        console.log(`    - Failed instances: ${beforeStats.failed} → 0`);
        console.log(`    - Sold instances: ${beforeStats.sold} → 0`);
        console.log(`    - Cancelled pending sales: ${cancelledSales}`);
        console.log(`    - Sell failures: ${beforeStats.sellFailures} → 0`);
        console.log("[+] ✅ Fresh start ready for ruins automation!");
    }

    /**
     * Force sell all pending ruins immediately (emergency function)
     */
    public forceSellAllPending(): void {
        console.log("[!] ===== FORCE SELLING ALL PENDING RUINS =====");

        if (this.pendingSales.size === 0) {
            console.log("[*] No pending sales to force");
            return;
        }

        console.log(`[*] Force selling ${this.pendingSales.size} pending instances...`);

        // Execute all pending sales immediately
        this.pendingSales.forEach((timeout, instanceId) => {
            clearTimeout(timeout);

            // Try to reconstruct the pointer (this may not work for stale pointers)
            try {
                const thisPtr = ptr(instanceId);
                console.log(`[!] Force selling ${instanceId}...`);
                this.executeAutomaticSelling(thisPtr, instanceId);
            } catch (error) {
                console.log(`[-] Failed to force sell ${instanceId}: ${error}`);
            }
        });

        this.pendingSales.clear();
        console.log("[+] Force selling completed");
    }

    /**
     * Hook InstantUpgrade method for enhanced monitoring and tracking
     */
    private hookInstantUpgrade(): void {
        if (!this.entityMethods.InstantUpgrade) {
            console.log("⚠️ InstantUpgrade method not available for hooking");
            return;
        }

        try {
            // Use Interceptor.attach for safer hooking without recursion issues
            const methodAddress = this.entityMethods.InstantUpgrade.handle;
            const self = this; // Capture 'this' context for use in interceptor

            Interceptor.attach(methodAddress, {
                onEnter: function(args) {
                    try {
                        // args[0] is 'this' pointer for the EntityController instance
                        const thisPtr = args[0];

                        // Create Il2Cpp object from pointer to call methods
                        const entityObj = new Il2Cpp.Object(thisPtr);
                        const thisEntity = entityObj as any;

                        // Get entity info before upgrade
                        const entityId = thisEntity.get_uniqueId();
                        const levelBefore = thisEntity.GetLevel();
                        const upgradeStartTime = Date.now();

                        console.log(`⚡ InstantUpgrade called on entity ${entityId} (level ${levelBefore})`);

                        // Store comprehensive tracking info
                        this.entityId = entityId;
                        this.levelBefore = levelBefore;
                        this.entityObj = entityObj;
                        this.upgradeStartTime = upgradeStartTime;

                        // Create or update tracker
                        const tracker: UpgradeTracker = {
                            entityId: entityId,
                            levelBefore: levelBefore,
                            upgradeStartTime: upgradeStartTime,
                            upgradeCallCount: (self.upgradeTrackers.get(entityId)?.upgradeCallCount || 0) + 1,
                            lastLevelCheck: levelBefore,
                            expectedLevel: levelBefore + 1
                        };

                        self.upgradeTrackers.set(entityId, tracker);

                    } catch (error) {
                        console.log(`❌ Error in InstantUpgrade onEnter: ${error}`);
                    }
                },
                onLeave: function(_retval) {
                    try {
                        if (this.entityObj && this.entityId !== undefined) {
                            const thisEntity = this.entityObj as any;
                            const levelAfter = thisEntity.GetLevel();
                            const upgradeEndTime = Date.now();
                            const upgradeDuration = upgradeEndTime - this.upgradeStartTime;

                            // Update tracker with results
                            const tracker = self.upgradeTrackers.get(this.entityId);
                            if (tracker) {
                                tracker.lastLevelCheck = levelAfter;
                            }

                            // Determine upgrade success
                            const upgradeSuccess = levelAfter > this.levelBefore;

                            if (upgradeSuccess) {
                                console.log(`✅ Entity ${this.entityId} upgraded successfully: ${this.levelBefore} → ${levelAfter} (${upgradeDuration}ms)`);
                            } else {
                                console.log(`⚠️ Entity ${this.entityId} InstantUpgrade called but level unchanged: ${this.levelBefore} (${upgradeDuration}ms)`);
                            }

                            // Execute callback if registered
                            const callback = self.upgradeCallbacks.get(this.entityId);
                            if (callback) {
                                callback(upgradeSuccess, levelAfter);
                                self.upgradeCallbacks.delete(this.entityId); // One-time callback
                            }
                        }
                    } catch (error) {
                        console.log(`❌ Error in InstantUpgrade onLeave: ${error}`);
                    }
                }
            });

            console.log("✅ InstantUpgrade method hooked with enhanced tracking");
        } catch (error) {
            console.log(`❌ Failed to hook InstantUpgrade: ${error}`);
        }
    }

    /**
     * Get all EntityController instances
     */
    public getAllInstances(): Il2Cpp.Object[] {
        try {
            if (!this.entityControllerClass) {
                console.log("❌ EntityController class not initialized");
                return [];
            }

            const instances = Il2Cpp.gc.choose(this.entityControllerClass);
            this.stats.totalInstances = instances.length;
            console.log(`🔍 Found ${instances.length} EntityController instances`);
            return instances;
        } catch (error) {
            console.log(`❌ Failed to get instances: ${error}`);
            return [];
        }
    }

    /**
     * Auto-upgrade ALL upgradeable entities with enhanced tracking and anti-debugging protection
     */
    public async autoUpgradeSelected(): Promise<number> {
        try {
            console.log("🚀 Starting auto-upgrade for ALL upgradeable entities (error-tolerant InstantUpgrade)...");

            const instances = this.getAllInstances();
            let upgradedCount = 0;
            let upgradeableCount = 0;
            let validInstances = 0;
            let invalidInstances = 0;

            console.log(`🔍 Processing ${instances.length} EntityController instances in batches...`);

            // Process instances in batches to reduce anti-debugging detection
            const batchSize = 50;
            const totalBatches = Math.ceil(instances.length / batchSize);

            for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
                const batchStart = batchIndex * batchSize;
                const batchEnd = Math.min(batchStart + batchSize, instances.length);
                const batch = instances.slice(batchStart, batchEnd);

                console.log(`📦 Processing batch ${batchIndex + 1}/${totalBatches} (instances ${batchStart}-${batchEnd - 1})`);

                // Process batch sequentially to handle async operations
                for (let batchLocalIndex = 0; batchLocalIndex < batch.length; batchLocalIndex++) {
                    const instance = batch[batchLocalIndex];
                    const globalIndex = batchStart + batchLocalIndex;

                    try {
                        // Step 1: Validate instance before any method calls
                        if (!this.isValidInstance(instance)) {
                            invalidInstances++;
                            if (globalIndex < 5) { // Log first few invalid instances for debugging
                                console.log(`⚠️ Instance ${globalIndex} is invalid, skipping`);
                            }
                            continue;
                        }

                        validInstances++;

                        // Step 2: Enhanced validation using multi-state checking
                        const validation = this.validateUpgradeState(instance, globalIndex);

                        if (validInstances <= 10) { // Log first few validations for debugging
                            console.log(`📊 Instance ${globalIndex}: Enhanced validation - ${validation.reason} (Level: ${validation.currentLevel}/${validation.maxLevel}, CanUpgrade: ${validation.canUpgrade})`);
                        }

                        if (!validation.canUpgrade) {
                            if (validInstances <= 10) {
                                console.log(`📋 Instance ${globalIndex}: Cannot upgrade - ${validation.reason}`);
                            }
                            continue;
                        }

                        if (validation.isUpgrading) {
                            if (validInstances <= 5) {
                                console.log(`⏳ Instance ${globalIndex}: Already upgrading, skipping`);
                            }
                            continue;
                        }

                        upgradeableCount++;
                        console.log(`🎯 Processing upgradeable entity ${globalIndex} (${upgradeableCount} upgradeable so far)`);

                        // Step 3: Enhanced upgrade with improved tracking
                        const upgradeResult = await this.enhancedUpgradeEntity(instance, globalIndex);
                        if (upgradeResult > 0) {
                            upgradedCount += upgradeResult;
                        }

                    } catch (error) {
                        const errorMsg = error instanceof Error ? error.message : String(error);
                        console.log(`❌ Error processing entity ${globalIndex}: ${errorMsg}`);
                    }
                }

                // Delay between batches to reduce anti-debugging detection
                if (batchIndex < totalBatches - 1) {
                    console.log(`⏳ Batch ${batchIndex + 1} complete, waiting 500ms before next batch...`);
                    const start = Date.now();
                    while (Date.now() - start < 500) { /* 500ms delay */ }
                }
            }

            // Comprehensive summary
            console.log(`📊 Processing Summary:`);
            console.log(`   Total instances found: ${instances.length}`);
            console.log(`   Valid instances: ${validInstances}`);
            console.log(`   Invalid instances: ${invalidInstances}`);
            console.log(`   Upgradeable instances: ${upgradeableCount}`);
            console.log(`   Total upgrade calls: ${upgradedCount}`);

            this.stats.selectedInstances = upgradeableCount;
            this.stats.upgradesPerformed += upgradedCount;

            console.log(`✅ Auto-upgrade complete! Upgradeable: ${upgradeableCount}, Total calls: ${upgradedCount}`);
            return upgradedCount;

        } catch (error) {
            const errorMsg = error instanceof Error ? error.message : String(error);
            console.log(`❌ Auto-upgrade failed: ${errorMsg}`);
            return 0;
        }
    }

    /**
     * Validate if an IL2CPP instance is safe to use with comprehensive testing
     */
    private isValidInstance(instance: Il2Cpp.Object): boolean {
        try {
            if (!instance) {
                return false;
            }

            // Check if instance handle is valid
            if (!instance.handle || instance.handle.isNull()) {
                return false;
            }

            // Try to access the class - this will fail for destroyed objects
            const instanceClass = instance.class;
            if (!instanceClass) {
                return false;
            }

            // Verify it's actually an EntityController
            if (instanceClass.name !== "EntityController") {
                return false;
            }

            // CRITICAL: Test method accessibility to filter destroyed objects
            try {
                const testMethod = instance.method("IsSelected");
                if (!testMethod || !testMethod.handle || testMethod.handle.isNull()) {
                    return false;
                }

                // Additional method accessibility test
                const testMethod2 = instance.method("GetLevel");
                if (!testMethod2 || !testMethod2.handle || testMethod2.handle.isNull()) {
                    return false;
                }

                // Test if we can safely access the method without triggering anti-debug
                if (testMethod.handle.readPointer().isNull()) {
                    return false;
                }

            } catch (methodError) {
                // Method access failed - this instance is destroyed/invalid
                return false;
            }

            return true;
        } catch (error) {
            // Any exception means the instance is invalid
            return false;
        }
    }

    /**
     * Enhanced upgrade validation using multiple state checks
     */
    private validateUpgradeState(instance: Il2Cpp.Object, instanceIndex: number): {
        canUpgrade: boolean;
        isUpgrading: boolean;
        currentLevel: number;
        maxLevel: number;
        upgradeTime: number;
        reason: string;
    } {
        const result = {
            canUpgrade: false,
            isUpgrading: false,
            currentLevel: 0,
            maxLevel: 0,
            upgradeTime: 0,
            reason: "Unknown"
        };

        try {
            // Get all validation methods
            const canUpgradeMethod = this.safeGetMethod(instance, "CanUpgrade");
            const isUpgradingMethod = this.safeGetMethod(instance, "IsUpgrading");
            const getLevelMethod = this.safeGetMethod(instance, "GetLevel");
            const getMaxUpgradeLevelMethod = this.safeGetMethod(instance, "GetMaxUpgradeLevel");
            const getUpgradeTimeMethod = this.safeGetMethod(instance, "GetUpgradeTime");

            if (!canUpgradeMethod || !getLevelMethod || !getMaxUpgradeLevelMethod) {
                result.reason = "Required validation methods not available";
                return result;
            }

            // Get current state
            result.currentLevel = this.safeInvokeMethod(getLevelMethod, [], `Instance ${instanceIndex} GetLevel (validation)`) || 0;
            result.maxLevel = this.safeInvokeMethod(getMaxUpgradeLevelMethod, [], `Instance ${instanceIndex} GetMaxUpgradeLevel (validation)`) || 0;
            result.canUpgrade = this.safeInvokeMethod(canUpgradeMethod, [true], `Instance ${instanceIndex} CanUpgrade(true) (validation)`) || false;

            if (isUpgradingMethod) {
                result.isUpgrading = this.safeInvokeMethod(isUpgradingMethod, [], `Instance ${instanceIndex} IsUpgrading (validation)`) || false;
            }

            if (getUpgradeTimeMethod) {
                result.upgradeTime = this.safeInvokeMethod(getUpgradeTimeMethod, [], `Instance ${instanceIndex} GetUpgradeTime (validation)`) || 0;
            }

            // Determine reason
            if (result.currentLevel >= result.maxLevel) {
                result.reason = "Already at max level";
            } else if (result.isUpgrading) {
                result.reason = "Currently upgrading";
            } else if (!result.canUpgrade) {
                result.reason = "Cannot upgrade (resources/requirements)";
            } else {
                result.reason = "Ready for upgrade";
            }

            return result;

        } catch (error) {
            result.reason = `Validation failed: ${error}`;
            return result;
        }
    }

    /**
     * Safely get a method from an instance with validation
     */
    private safeGetMethod(instance: Il2Cpp.Object, methodName: string): Il2Cpp.Method | null {
        try {
            if (!this.isValidInstance(instance)) {
                return null;
            }

            const method = instance.method(methodName);
            if (!method) {
                return null;
            }

            // Verify method handle is valid
            if (!method.handle || method.handle.isNull()) {
                return null;
            }

            return method;
        } catch (error) {
            return null;
        }
    }

    /**
     * Safely invoke a method with basic error handling
     */
    private safeInvokeMethod(method: Il2Cpp.Method, args: any[] = [], context: string = "Unknown"): any {
        try {
            if (!method) {
                console.log(`⚠️ ${context}: Method is null`);
                return null;
            }

            const result = method.invoke(...args);
            return result;

        } catch (error) {
            const errorMsg = error instanceof Error ? error.message : String(error);
            console.log(`❌ ${context}: Method invocation failed - ${errorMsg}`);
            return null;
        }
    }

    /**
     * Enhanced upgrade entity with improved tracking, validation, and success detection
     */
    private async enhancedUpgradeEntity(instance: Il2Cpp.Object, instanceIndex: number): Promise<number> {
        try {
            let upgradedCount = 0;

            // Get entity ID for enhanced tracking
            const getUniqueIdMethod = this.safeGetMethod(instance, "get_uniqueId");
            const entityId = getUniqueIdMethod ? this.safeInvokeMethod(getUniqueIdMethod, [], `Instance ${instanceIndex} get_uniqueId (enhanced)`) : `entity_${instanceIndex}`;

            // Get all required methods safely
            const canUpgradeMethod = this.safeGetMethod(instance, "CanUpgrade");
            const getLevelMethod = this.safeGetMethod(instance, "GetLevel");
            const getMaxUpgradeLevelMethod = this.safeGetMethod(instance, "GetMaxUpgradeLevel");
            const getMaxLevelMethod = this.safeGetMethod(instance, "GetMaxLevel");

            if (!canUpgradeMethod || !getLevelMethod || !getMaxUpgradeLevelMethod) {
                console.log(`⚠️ Instance ${instanceIndex}: Required upgrade methods not accessible`);
                return 0;
            }

            // Get initial level information
            let currentLevel = this.safeInvokeMethod(getLevelMethod, [], `Instance ${instanceIndex} GetLevel (initial)`);
            const maxUpgradeLevel = this.safeInvokeMethod(getMaxUpgradeLevelMethod, [], `Instance ${instanceIndex} GetMaxUpgradeLevel`);
            const maxLevel = getMaxLevelMethod ? this.safeInvokeMethod(getMaxLevelMethod, [], `Instance ${instanceIndex} GetMaxLevel`) : maxUpgradeLevel;

            if (currentLevel === null || maxUpgradeLevel === null) {
                console.log(`❌ Instance ${instanceIndex}: Failed to get level information`);
                return 0;
            }

            // Use the higher max level but stop at max-1 (leave final upgrade for manual)
            const trueMaxLevel = Math.max(maxUpgradeLevel || 0, maxLevel || 0);
            const actualMaxLevel = trueMaxLevel - 1; // Stop one level below maximum

            console.log(`📊 Instance ${instanceIndex}: Enhanced upgrade from level ${currentLevel}/${actualMaxLevel} (entity: ${entityId})`);

            // Check if already at target level (max-1)
            if (currentLevel >= actualMaxLevel) {
                console.log(`🎯 Instance ${instanceIndex}: Building at target level ${currentLevel}/${actualMaxLevel} (ready for manual final upgrade to ${trueMaxLevel})`);
                return 0;
            }

            // Enhanced upgrade loop with improved tracking
            let upgradeAttempts = 0;
            const maxAttempts = Math.min(actualMaxLevel - currentLevel, 10); // Limit attempts

            while (currentLevel < actualMaxLevel && upgradeAttempts < maxAttempts) {
                // Enhanced pre-upgrade validation
                const validation = this.validateUpgradeState(instance, instanceIndex);
                if (!validation.canUpgrade) {
                    console.log(`⚠️ Instance ${instanceIndex}: Enhanced validation failed - ${validation.reason}`);
                    break;
                }

                const levelBefore = currentLevel;
                console.log(`⚡ Instance ${instanceIndex}: Enhanced InstantUpgrade (attempt ${upgradeAttempts + 1}) - Level ${levelBefore}/${actualMaxLevel}`);

                // PROVEN WORKING: Use error-tolerant InstantUpgrade approach
                const upgradeStartTime = Date.now();

                // Call InstantUpgrade with error tolerance (proven to work despite errors)
                let upgradeCallSuccess: boolean | "maybe" = false;
                let errorMessage = "";

                try {
                    // Direct InstantUpgrade call - this is what actually works
                    this.safeInvokeInstanceMethod(instance, "InstantUpgrade", [], `Instance ${instanceIndex} InstantUpgrade (proven method)`);
                    upgradeCallSuccess = true;
                    console.log(`✅ Instance ${instanceIndex}: InstantUpgrade called successfully`);

                } catch (upgradeError) {
                    const errorMsg = upgradeError instanceof Error ? upgradeError.message : String(upgradeError);
                    errorMessage = errorMsg;

                    // These errors are expected due to anti-debugging, but upgrade still works
                    if (errorMsg.includes("breakpoint triggered") ||
                        errorMsg.includes("access violation") ||
                        errorMsg.includes("illegal instruction") ||
                        errorMsg.includes("abort was called")) {

                        console.log(`⚠️ Instance ${instanceIndex}: Expected anti-debug error (${errorMsg.split(' ')[0]}) - checking if upgrade worked anyway...`);
                        upgradeCallSuccess = "maybe"; // We'll verify with level check

                    } else {
                        console.log(`❌ Instance ${instanceIndex}: Unexpected InstantUpgrade error: ${errorMsg}`);
                        break; // Stop trying this entity
                    }
                }

                upgradedCount++;

                // Verify upgrade success with proper timing
                console.log(`⏳ Instance ${instanceIndex}: Verifying upgrade completion...`);
                await new Promise(resolve => setTimeout(resolve, 1200)); // Proven timing for upgrade processing

                // Check for level change
                const finalLevel = this.safeInvokeMethod(getLevelMethod, [], `Instance ${instanceIndex} GetLevel (verification)`);
                let upgradeDetected = false;

                if (finalLevel !== null && finalLevel > levelBefore) {
                    upgradeDetected = true;
                    const upgradeDuration = Date.now() - upgradeStartTime;
                    console.log(`🎉 Instance ${instanceIndex}: UPGRADE CONFIRMED! ${levelBefore} → ${finalLevel} (${upgradeDuration}ms)`);

                    // If this was a "maybe" success due to anti-debug error, now we know it worked
                    if (upgradeCallSuccess === "maybe") {
                        console.log(`💡 Instance ${instanceIndex}: Anti-debug error was harmless - upgrade succeeded! (${errorMessage.split(' ')[0]})`);
                    }

                } else if (finalLevel === levelBefore) {
                    console.log(`📋 Instance ${instanceIndex}: Level unchanged (${levelBefore}) - upgrade may need more time`);
                }

                // Update current level
                currentLevel = finalLevel || currentLevel;

                // Check if we've reached target level
                if (finalLevel >= actualMaxLevel) {
                    console.log(`🎯 Instance ${instanceIndex}: Reached target level ${finalLevel}/${actualMaxLevel} (final upgrade to ${trueMaxLevel} left for manual)`);
                    break;
                }

                upgradeAttempts++;
            }

            console.log(`📊 Instance ${instanceIndex}: Enhanced upgrade complete. Final level: ${currentLevel}/${actualMaxLevel}, InstantUpgrade calls: ${upgradedCount}, Attempts: ${upgradeAttempts}`);
            return upgradedCount;

        } catch (error) {
            console.log(`❌ Instance ${instanceIndex}: Upgrade process failed - ${error}`);
            return 0;
        }
    }

    /**
     * Safely invoke an instance method using direct method call (bypasses some anti-debug)
     */
    private safeInvokeInstanceMethod(instance: Il2Cpp.Object, methodName: string, args: any[] = [], context: string = "Unknown"): any {
        try {
            if (!instance) {
                console.log(`⚠️ ${context}: Instance is null`);
                return null;
            }

            // Validate instance first
            if (!this.isValidInstance(instance)) {
                console.log(`⚠️ ${context}: Instance is invalid for method '${methodName}'`);
                return null;
            }

            console.log(`🔧 ${context}: Attempting ${methodName} call...`);

            // Method 1: Try direct method call (preferred for InstantUpgrade)
            if (methodName === "InstantUpgrade") {
                try {
                    // Use the entity as 'this' and call the method directly
                    const entityAsAny = instance as any;
                    const result = entityAsAny.InstantUpgrade();
                    console.log(`✅ ${context}: Direct InstantUpgrade call successful, result: ${result}`);
                    return result;
                } catch (directError) {
                    console.log(`⚠️ ${context}: Direct call failed: ${directError}, trying method invoke...`);
                }
            }

            // Method 2: Try standard method invoke
            try {
                const method = instance.method(methodName);
                if (!method) {
                    console.log(`⚠️ ${context}: Method '${methodName}' not found on instance`);
                    return null;
                }

                // Verify method handle
                if (!method.handle || method.handle.isNull()) {
                    console.log(`⚠️ ${context}: Method '${methodName}' has invalid handle`);
                    return null;
                }

                const result = method.invoke(...args);
                console.log(`✅ ${context}: Method invoke successful, result: ${result}`);
                return result;

            } catch (invokeError) {
                console.log(`❌ ${context}: Method invoke failed: ${invokeError}`);
            }

            // Method 3: Try using the cached method reference
            if (methodName === "InstantUpgrade" && this.entityMethods.InstantUpgrade) {
                try {
                    console.log(`🔧 ${context}: Trying cached method reference...`);
                    const result = this.entityMethods.InstantUpgrade.invoke(instance);
                    console.log(`✅ ${context}: Cached method call successful, result: ${result}`);
                    return result;
                } catch (cachedError) {
                    console.log(`❌ ${context}: Cached method failed: ${cachedError}`);
                }
            }

            console.log(`❌ ${context}: All ${methodName} call methods failed`);
            return null;

        } catch (error) {
            const errorMsg = error instanceof Error ? error.message : String(error);
            console.log(`❌ ${context}: Instance method '${methodName}' invocation failed - ${errorMsg}`);
            return null;
        }
    }

    /**
     * Setup global automation functions
     */
    private setupGlobalFunctions(): void {
        console.log("🤖 Setting up global automation functions...");

        // Make core methods available globally using globalThis (proper Frida global scope)
        try {
            // EntityController functions
            (globalThis as any).getAllEntityInstances = () => this.getAllInstances();
            (globalThis as any).autoUpgradeSelected = () => this.autoUpgradeSelected();

            // GoodyHutHelper functions
            (globalThis as any).getCollectionStats = () => this.getCollectionStats();
            (globalThis as any).startRuinsCollection = () => this.startRuinsCollection();
            (globalThis as any).stopRuinsCollection = () => this.stopRuinsCollection();
            (globalThis as any).clearProcessedRuins = () => this.clearProcessedRuins();
            (globalThis as any).forceSellAllPending = () => this.forceSellAllPending();

            console.log("✅ Core global functions assigned to globalThis");
        } catch (error) {
            console.log(`⚠️ globalThis assignment failed, trying alternative: ${error}`);

            // Fallback: try direct assignment to global scope
            try {
                // EntityController functions
                (global as any).getAllEntityInstances = () => this.getAllInstances();
                (global as any).autoUpgradeSelected = () => this.autoUpgradeSelected();

                // GoodyHutHelper functions
                (global as any).getCollectionStats = () => this.getCollectionStats();
                (global as any).startRuinsCollection = () => this.startRuinsCollection();
                (global as any).stopRuinsCollection = () => this.stopRuinsCollection();
                (global as any).clearProcessedRuins = () => this.clearProcessedRuins();
                (global as any).forceSellAllPending = () => this.forceSellAllPending();

                console.log("✅ Core global functions assigned to global");
            } catch (globalError) {
                console.log(`⚠️ global assignment also failed: ${globalError}`);
                console.log("💡 Functions will be available through gameAutomation instance");
            }
        }

        console.log("✅ Unified automation functions setup complete!");
        console.log("📋 Available EntityController functions:");
        console.log("   - getAllEntityInstances() - Get all EntityController instances");
        console.log("   - autoUpgradeSelected() - Auto-upgrade with error-tolerant InstantUpgrade calls");
        console.log("📋 Available GoodyHutHelper functions:");
        console.log("   - getCollectionStats() - Get ruins collection & selling statistics");
        console.log("   - startRuinsCollection() - Start FULLY AUTOMATED ruins collection & selling");
        console.log("   - stopRuinsCollection() - Stop automatic selling (collection hooks remain active)");
        console.log("   - clearProcessedRuins() - Clear processed ruins and selling cache");
        console.log("   - forceSellAllPending() - Force sell all pending ruins immediately");
    }

    /**
     * List large modules for debugging
     */
    private listLargeModules(): void {
        try {
            console.log("💡 Large modules (>1MB) in process:");
            const modules: Array<{name: string, size: number, sizeMB: string, base: string}> = [];

            Process.enumerateModules().forEach((module) => {
                if (module.size > 1000000) { // > 1MB
                    modules.push({
                        name: module.name,
                        size: module.size,
                        sizeMB: (module.size/1024/1024).toFixed(1),
                        base: module.base.toString()
                    });
                }
            });

            // Sort by size (largest first)
            modules.sort((a, b) => b.size - a.size);

            modules.forEach((mod, index) => {
                console.log(`   ${index + 1}. ${mod.name} (${mod.sizeMB}MB) at ${mod.base}`);
            });
        } catch (error) {
            console.log("   Could not enumerate modules");
        }
    }

    /**
     * List available assemblies for debugging
     */
    private listAvailableAssemblies(): void {
        try {
            console.log("💡 Available assemblies:");
            const assemblies = Il2Cpp.domain.assemblies;
            assemblies.slice(0, 10).forEach((assembly, index) => {
                console.log(`   ${index}: ${assembly.name}`);
            });
        } catch (error) {
            console.log("   Could not enumerate assemblies");
        }
    }

    /**
     * List available classes for debugging
     */
    private listAvailableClasses(): void {
        if (!this.assemblyImage) return;

        try {
            console.log("💡 Available classes (first 10):");
            const classes = this.assemblyImage.classes;
            for (let i = 0; i < Math.min(10, classes.length); i++) {
                console.log(`   ${i}: ${classes[i].name}`);
            }

            // Look for classes containing "Entity" or "Controller"
            const entityClasses = classes.filter(cls =>
                cls.name.toLowerCase().includes('entity') ||
                cls.name.toLowerCase().includes('controller') ||
                cls.name.toLowerCase().includes('goody') ||
                cls.name.toLowerCase().includes('hut')
            );

            if (entityClasses.length > 0) {
                console.log("🔍 Found entity/controller/goody related classes:");
                entityClasses.forEach((cls, index) => {
                    console.log(`   ${index}: ${cls.name}`);
                });
            }
        } catch (error) {
            console.log("   Could not enumerate classes");
        }
    }
}

// Script execution entry point
Il2Cpp.perform(async () => {
    console.log("🚀 Unified Game Automation - Il2Cpp bridge context established");

    try {
        // Wait for game to be fully loaded
        await new Promise(resolve => setTimeout(resolve, 5000));

        const automation = new UnifiedGameAutomation();
        const initialized = await automation.initialize();

        if (initialized) {
            console.log("✅ Unified game automation ready!");

            // Make automation instance available globally for debugging
            try {
                (globalThis as any).gameAutomation = automation;
                (globalThis as any).entityHook = automation; // Backward compatibility
                console.log("✅ Automation instance available as 'gameAutomation' and 'entityHook'");
            } catch (error) {
                console.log(`⚠️ Could not assign to globalThis: ${error}`);
                try {
                    (global as any).gameAutomation = automation;
                    (global as any).entityHook = automation; // Backward compatibility
                    console.log("✅ Automation instance available as 'gameAutomation' and 'entityHook' (via global)");
                } catch (globalError) {
                    console.log(`⚠️ Could not assign to global either: ${globalError}`);
                }
            }

            // Test global function accessibility
            setTimeout(() => {
                console.log("🧪 Testing global function accessibility...");
                try {
                    if (typeof (globalThis as any).getAllEntityInstances === 'function') {
                        console.log("✅ getAllEntityInstances is accessible globally");
                    } else {
                        console.log("❌ getAllEntityInstances is not accessible globally");
                    }

                    if (typeof (globalThis as any).getCollectionStats === 'function') {
                        console.log("✅ getCollectionStats is accessible globally");
                    } else {
                        console.log("❌ getCollectionStats is not accessible globally");
                    }
                } catch (testError) {
                    console.log(`⚠️ Global function test failed: ${testError}`);
                }
            }, 1000);

            // Usage instructions
            console.log("💡 EntityController Usage:");
            console.log("   - autoUpgradeSelected() to upgrade entities to max-1 level");
            console.log("   - getAllEntityInstances() to get all instances");
            console.log("💡 GoodyHutHelper Usage (FULLY AUTOMATED):");
            console.log("   - 🏺 Collection: Automatic when CanCollect() returns true");
            console.log("   - 💰 Selling: Automatic 1-3 seconds after collection");
            console.log("   - 🧹 Cleanup: cleanUp flag set automatically at offset 0x30");
            console.log("   - getCollectionStats() to see collection & selling statistics");
            console.log("   - startRuinsCollection() to see system status (already active)");
            console.log("   - clearProcessedRuins() to reset all caches");
            console.log("   - forceSellAllPending() for emergency selling");
            console.log("💡 Use gameAutomation.methodName() if global functions don't work");
            console.log("🎯 FULLY AUTOMATED: Ruins are collected AND sold without manual intervention!");
        } else {
            console.log("❌ Failed to initialize unified game automation");
        }

    } catch (error) {
        console.log(`❌ Fatal error: ${error}`);
    }
});
